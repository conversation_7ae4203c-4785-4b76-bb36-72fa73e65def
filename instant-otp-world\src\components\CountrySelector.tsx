import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Globe } from "lucide-react";
import { type Country } from "@/lib/api";

interface CountrySelectorProps {
  countries: Record<string, Country>;
  selectedCountry: string | null;
  onSelect: (countryId: string) => void;
  isLoading: boolean;
}

export const CountrySelector = ({
  countries,
  selectedCountry,
  onSelect,
  isLoading,
}: CountrySelectorProps) => {
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Globe className="w-5 h-5" />
            <span>Loading countries...</span>
          </CardTitle>
        </CardHeader>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Globe className="w-5 h-5" />
          <span>Select Country</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-2 max-h-96 overflow-y-auto">
        {Object.entries(countries).map(([countryId, country]) => (
          <Button
            key={countryId}
            variant={selectedCountry === countryId ? "default" : "ghost"}
            className="w-full justify-start h-auto p-3"
            onClick={() => onSelect(countryId)}
          >
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center space-x-3">
                <div className="text-left">
                  <div className="font-medium">{country.name}</div>
                  <div className="text-xs text-muted-foreground">
                    {country.available} numbers available
                  </div>
                </div>
              </div>
              <Badge variant="secondary" className="text-xs">
                {country.iso}
              </Badge>
            </div>
          </Button>
        ))}
      </CardContent>
    </Card>
  );
};
