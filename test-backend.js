const axios = require("axios");

async function testBackend() {
  try {
    console.log("Testing backend API...");

    // Test countries endpoint
    console.log("\n1. Testing /api/countries...");
    const countriesResponse = await axios.get(
      "http://localhost:3001/api/countries"
    );
    console.log("Countries response status:", countriesResponse.status);
    console.log(
      "Number of countries:",
      Object.keys(countriesResponse.data).length
    );

    // Test operators endpoint with USA
    console.log("\n2. Testing /api/operators/usa...");
    const operatorsResponse = await axios.get(
      "http://localhost:3001/api/operators/usa"
    );
    console.log("Operators response status:", operatorsResponse.status);
    console.log("Operators data:", operatorsResponse.data);

    console.log("\nBackend is working correctly!");
  } catch (error) {
    console.error("Backend test failed:", error.message);
    if (error.response) {
      console.error("Response status:", error.response.status);
      console.error("Response data:", error.response.data);
    }
  }
}

testBackend();
