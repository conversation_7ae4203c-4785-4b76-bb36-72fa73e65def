import { useState, useEffect } from "react";
import { CountrySelector } from "@/components/CountrySelector";
import { PhoneNumberDisplay } from "@/components/PhoneNumberDisplay";
import { SMSViewer } from "@/components/SMSViewer";
import { RefreshCw, Shield, Zap, Globe } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import axios, { AxiosError } from "axios";
import type { AxiosResponse } from "axios";
import {
  fetchCountries,
  fetchOperators,
  createOrder,
  fetchMessages,
  type Country,
  type OrderResponse,
  type Operator,
  type Message,
} from "@/lib/api";
import { useToast } from "@/components/ui/use-toast";

const Index = () => {
  const { toast } = useToast();
  const [selectedCountry, setSelectedCountry] = useState<string | null>(null);
  const [selectedOperator, setSelectedOperator] = useState<string | null>(null);
  const [currentOrder, setCurrentOrder] = useState<OrderResponse | null>(null);
  const queryClient = useQueryClient();

  // Fetch operators when country changes
  const { data: operators, isLoading: isLoadingOperators } = useQuery({
    queryKey: ["operators", selectedCountry],
    queryFn: () =>
      selectedCountry ? fetchOperators(selectedCountry) : Promise.resolve([]),
    enabled: !!selectedCountry,
  });

  // Fetch countries
  const { data: countries, isLoading: isLoadingCountries } = useQuery({
    queryKey: ["countries"],
    queryFn: fetchCountries,
  });

  // Fetch messages when there's an active order
  const { data: messages = [], isLoading: isLoadingMessages } = useQuery({
    queryKey: ["messages", currentOrder?.id],
    queryFn: () =>
      currentOrder?.id ? fetchMessages(currentOrder.id) : Promise.resolve([]),
    enabled: !!currentOrder?.id,
    refetchInterval: 5000, // Poll every 5 seconds
    refetchIntervalInBackground: true,
  });

  // Create order mutation
  const orderMutation = useMutation<
    OrderResponse,
    AxiosError,
    { country: string; operator: string; product: string }
  >({
    mutationFn: (params) => createOrder(params),
    onSuccess: (data) => {
      setCurrentOrder(data);
      toast({
        title: "Success",
        description: "Successfully purchased phone number",
      });
    },
    onError: (error: AxiosError) => {
      // Show a more detailed error message to the user
      let errorMessage = "Failed to purchase phone number. Please try again.";
      if (
        error.response?.data &&
        typeof error.response.data === "object" &&
        "error" in error.response.data
      ) {
        errorMessage = (error.response.data as { error: string }).error;
      } else if (
        error.response?.data &&
        typeof error.response.data === "object" &&
        "message" in error.response.data
      ) {
        errorMessage = (error.response.data as { message: string }).message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    },
  });

  const handleCountrySelect = (countryId: string) => {
    setSelectedCountry(countryId);
    setSelectedOperator(null);
    setCurrentOrder(null);
  };

  const handleOperatorSelect = (operatorId: string) => {
    setSelectedOperator(operatorId);
  };

  const handlePurchase = async () => {
    if (!selectedCountry || !selectedOperator) {
      toast({
        title: "Error",
        description: "Please select both country and operator",
        variant: "destructive",
      });
      return;
    }

    try {
      // Get the selected operator's data
      const operator = operators?.find((op) => op.id === selectedOperator);

      if (!operator || !operator.products?.length) {
        throw new Error("No products available for the selected operator");
      }

      // Use the first available product
      const product = operator.products[0];

      const orderParams = {
        country: selectedCountry,
        operator: selectedOperator,
        product: product,
      };

      // Proceed with the mutation
      orderMutation.mutate(orderParams);
    } catch (error) {
      toast({
        title: "Error",
        description:
          error instanceof Error ? error.message : "Failed to place order",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto py-8">
        <div className="mb-8 text-center">
          <h1 className="text-4xl font-bold mb-4">Instant OTP World</h1>
          <p className="text-muted-foreground">
            Get instant phone numbers for OTP verification
          </p>
        </div>

        <div className="grid gap-8 md:grid-cols-2">
          <div className="space-y-6">
            <div className="p-6 border rounded-lg">
              <h2 className="text-2xl font-semibold mb-4">Select Location</h2>
              <CountrySelector
                countries={countries || {}}
                selectedCountry={selectedCountry}
                onSelect={handleCountrySelect}
                isLoading={isLoadingCountries}
              />
            </div>

            {selectedCountry && (
              <div className="space-y-4 p-6 border rounded-lg">
                <div>
                  <label
                    className="block text-sm font-medium mb-2"
                    htmlFor="operator"
                  >
                    Select Operator
                  </label>
                  {operators?.length === 0 ? (
                    <div className="text-sm text-muted-foreground p-2 border rounded bg-muted">
                      No operators available for the selected country.
                    </div>
                  ) : (
                    <Select
                      value={selectedOperator || ""}
                      onValueChange={handleOperatorSelect}
                      disabled={isLoadingOperators}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue
                          placeholder={
                            isLoadingOperators
                              ? "Loading operators..."
                              : "Select an operator"
                          }
                        />
                      </SelectTrigger>
                      <SelectContent>
                        {operators?.map((operator) => (
                          <SelectItem
                            key={operator.id}
                            value={operator.id}
                            disabled={!operator.products?.length}
                            className="flex justify-between items-center"
                          >
                            <span>{operator.name}</span>
                            {!operator.products?.length && (
                              <span className="text-xs text-muted-foreground ml-2">
                                (No products)
                              </span>
                            )}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                </div>

                {operators?.some((op) => op.products?.length > 0) ? (
                  <Button
                    className="w-full"
                    onClick={handlePurchase}
                    disabled={orderMutation.isPending || !selectedOperator}
                  >
                    {orderMutation.isPending
                      ? "Purchasing..."
                      : "Purchase Number"}
                  </Button>
                ) : operators && operators.length > 0 ? (
                  <div className="text-sm text-yellow-600 bg-yellow-50 p-3 rounded-md border border-yellow-200">
                    <p className="font-medium">No products available</p>
                    <p className="text-xs mt-1">
                      There are no available products for the operators in this
                      country. Please try a different country or contact
                      support.
                    </p>
                  </div>
                ) : null}
              </div>
            )}
          </div>

          <div className="space-y-6">
            <div className="p-6 border rounded-lg">
              <h2 className="text-2xl font-semibold mb-4">Your Number</h2>
              <PhoneNumberDisplay
                number={currentOrder?.phone || null}
                isLoading={orderMutation.isPending}
              />
            </div>

            <div className="p-6 border rounded-lg">
              <h2 className="text-2xl font-semibold mb-4">Messages</h2>
              <SMSViewer
                messages={messages}
                isLoading={isLoadingMessages && !!currentOrder?.id}
              />
            </div>
          </div>
        </div>

        <div className="mt-12 grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="p-6 border rounded-lg text-center">
            <Globe className="mx-auto h-8 w-8 mb-2" />
            <h3 className="font-semibold">Global Coverage</h3>
            <p className="text-sm text-muted-foreground">
              Numbers from 100+ countries
            </p>
          </div>
          <div className="p-6 border rounded-lg text-center">
            <Zap className="mx-auto h-8 w-8 mb-2" />
            <h3 className="font-semibold">Instant Delivery</h3>
            <p className="text-sm text-muted-foreground">
              Get numbers in seconds
            </p>
          </div>
          <div className="p-6 border rounded-lg text-center">
            <Shield className="mx-auto h-8 w-8 mb-2" />
            <h3 className="font-semibold">Secure & Private</h3>
            <p className="text-sm text-muted-foreground">
              Your data is protected
            </p>
          </div>
          <div className="p-6 border rounded-lg text-center">
            <RefreshCw className="mx-auto h-8 w-8 mb-2" />
            <h3 className="font-semibold">Auto Updates</h3>
            <p className="text-sm text-muted-foreground">
              Real-time SMS delivery
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Index;
