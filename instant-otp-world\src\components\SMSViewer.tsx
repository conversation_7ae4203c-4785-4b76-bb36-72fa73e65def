import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { MessageSquare, Refresh<PERSON>w, Co<PERSON>, CheckCircle } from "lucide-react";
import { useState, useEffect } from "react";
import { useToast } from "@/components/ui/use-toast";

interface Message {
  id: string;
  code: string;
  text: string;
  sender: string;
  time: string;
}

interface SMSViewerProps {
  messages: Message[];
  isLoading: boolean;
}

export const SMSViewer = ({ messages, isLoading }: SMSViewerProps) => {
  const [copiedCodes, setCopiedCodes] = useState<Set<string>>(new Set());
  const { toast } = useToast();

  const handleCopyCode = async (message: Message) => {
    try {
      await navigator.clipboard.writeText(message.code);
      setCopiedCodes((prev) => new Set([...prev, message.id]));
      toast({
        title: "Code copied!",
        description: `Verification code copied to clipboard`,
      });
      setTimeout(() => {
        setCopiedCodes((prev) => {
          const newSet = new Set(prev);
          newSet.delete(message.id);
          return newSet;
        });
      }, 2000);
    } catch (err) {
      toast({
        title: "Failed to copy",
        description: "Please copy the code manually",
        variant: "destructive",
      });
    }
  };

  const formatTime = (timeStr: string) => {
    const date = new Date(timeStr);
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <MessageSquare className="w-5 h-5" />
            <span>Messages</span>
            {messages.length > 0 && (
              <Badge variant="secondary" className="ml-2">
                {messages.length}
              </Badge>
            )}
          </CardTitle>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="text-center py-8">
            <div className="animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
            <p className="text-muted-foreground">Checking for new messages...</p>
          </div>
        ) : messages.length === 0 ? (
          <div className="text-center py-8">
            <MessageSquare className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">No messages yet</p>
          </div>
        ) : (
          <div className="space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className="bg-muted rounded-lg p-4 space-y-2"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline">{message.sender}</Badge>
                    <span className="text-xs text-muted-foreground">
                      {formatTime(message.time)}
                    </span>
                  </div>
                  <Button
                    onClick={() => handleCopyCode(message)}
                    variant="ghost"
                    size="sm"
                    className="h-8 px-2"
                  >
                    {copiedCodes.has(message.id) ? (
                      <CheckCircle className="w-4 h-4 text-green-500" />
                    ) : (
                      <Copy className="w-4 h-4" />
                    )}
                  </Button>
                </div>
                <p className="text-sm">{message.text}</p>
                <div className="bg-background rounded p-2">
                  <code className="text-sm font-mono">{message.code}</code>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
