const express = require("express");
const dotenv = require("dotenv");
const axios = require("axios");
const cors = require("cors");

// Load environment variables
dotenv.config();

const app = express();
app.use(cors());
app.use(express.json());
const PORT = process.env.PORT || 3001;
const API_KEY = process.env.FIVESIM_API_KEY;
const BASE_URL = "https://5sim.net/v1";

// Create axios instance with default config
const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    Authorization: `Bearer ${API_KEY}`,
    Accept: "application/json",
  },
});

// GET /api/countries
app.get("/api/countries", async (req, res) => {
  try {
    const response = await api.get("/guest/countries");
    const countriesData = response.data;

    // Transform the data while keeping it as an object
    const transformedCountries = Object.entries(countriesData).reduce(
      (acc, [countryCode, data]) => {
        // Extract the count from the virtual21 property if it exists
        const count = data.virtual21?.count || 0;

        acc[countryCode] = {
          id: countryCode,
          name: data.text_en || "Unknown",
          available: count,
          iso: data.iso?.code || countryCode,
          prefix: data.prefix?.code || "",
        };
        return acc;
      },
      {}
    );

    res.json(transformedCountries);
  } catch (error) {
    console.error(
      "Error fetching countries:",
      error.response?.data || error.message
    );
    res.status(500).json({ error: "Failed to fetch countries" });
  }
});

// GET /api/operators/:country
app.get("/api/operators/:country", async (req, res) => {
  const { country } = req.params;
  console.log("Received request for operators, country:", country);
  try {
    const response = await api.get(`/guest/prices?country=${country}`);
    console.log("5sim API response:", response.data);
    const countryData = response.data[country] || {};
    console.log("Country data:", countryData);

    // Transform the object into an array of operators
    const operators = Object.entries(countryData).map(([operatorId, data]) => {
      // The data structure from 5sim has virtual operators as keys
      // Each virtual operator contains product information
      const virtualOperators = Object.keys(data || {});
      const hasProducts = virtualOperators.length > 0;

      const operator = {
        id: operatorId,
        name: operatorId.charAt(0).toUpperCase() + operatorId.slice(1), // Capitalize first letter
        products: hasProducts ? ["virtual"] : [], // Simplified - just indicate if products are available
      };
      console.log("Transformed operator:", operator);
      return operator;
    });

    console.log("Sending operators:", operators);
    res.json(operators);
  } catch (error) {
    console.error(
      "Error fetching operators:",
      error.response?.data || error.message
    );
    res.status(500).json({ error: "Failed to fetch operators and products" });
  }
});

// POST /api/order
app.post("/api/order", async (req, res) => {
  const { country, operator, product } = req.body;
  console.log("Received order request:", { country, operator, product });

  try {
    let operatorId = operator;
    let productId = product;

    // If operator or product not specified, get the first available ones
    if (!operatorId || !productId) {
      console.log(
        "Fetching operators from 5sim API to find available options..."
      );
      const operatorsResponse = await api.get(
        `/guest/prices?country=${country}`
      );
      console.log("5sim API prices response:", operatorsResponse.data);
      const countryData = operatorsResponse.data[country] || {};
      console.log("Country data:", countryData);

      // Get the first available operator and product
      const firstOperator = Object.entries(countryData)[0];
      if (!firstOperator) {
        console.log("No operators found for country:", country);
        return res
          .status(400)
          .json({ error: "No operators available for this country" });
      }

      const [firstOperatorId, operatorData] = firstOperator;
      operatorId = operatorId || firstOperatorId;
      console.log("Selected operator:", operatorId, operatorData);

      // Get the first virtual operator (like virtual21, virtual34, etc.)
      const virtualOperators = Object.keys(operatorData || {});
      if (virtualOperators.length === 0) {
        console.log("No virtual operators found for operator:", operatorId);
        return res
          .status(400)
          .json({ error: "No virtual operators available for this operator" });
      }

      const firstVirtualOperator = virtualOperators[0];
      productId = productId || firstVirtualOperator;
      console.log("Selected virtual operator (product):", productId);
    }

    // Create order with specified or first available operator and product
    console.log("Creating order with:", { country, operatorId, productId });
    const response = await api.post(
      `/user/buy/activation/${country}/${operatorId}/${productId}`
    );
    console.log("5sim API order response:", response.data);

    // Transform the response to match frontend expectations
    const transformedResponse = {
      id: response.data.id?.toString() || "",
      phone: response.data.phone || "",
      status: response.data.status || "pending",
      // Include original data for debugging
      original: response.data,
    };

    console.log("Transformed response:", transformedResponse);
    res.json(transformedResponse);
  } catch (error) {
    console.error("Error placing order:");
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      console.error("API Error Response:", {
        status: error.response.status,
        data: error.response.data,
        headers: error.response.headers,
      });
      res.status(error.response.status).json({
        error: error.response.data.message || "Failed to buy phone number",
        details: error.response.data,
      });
    } else if (error.request) {
      // The request was made but no response was received
      console.error("No response received:", error.request);
      res.status(500).json({ error: "No response received from 5sim API" });
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error("Error setting up request:", error.message);
      res.status(500).json({ error: "Failed to make request to 5sim API" });
    }
  }
});

// GET /api/messages/:id
app.get("/api/messages/:id", async (req, res) => {
  const { id } = req.params;
  try {
    const response = await api.get(`/user/check/${id}`);
    res.json(response.data);
  } catch (error) {
    console.error(
      "Error fetching messages:",
      error.response?.data || error.message
    );
    res.status(500).json({ error: "Failed to fetch messages" });
  }
});

app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
