import axios from "axios";

const API_BASE_URL = "http://localhost:3000/api";

const api = axios.create({
  baseURL: API_BASE_URL,
});

export interface Country {
  id: string;
  name: string;
  available: number;
  iso: string;
  prefix: string;
}

export interface Operator {
  id: string;
  name: string;
  products: string[];
}

export interface OrderResponse {
  id: string;
  phone: string;
  status: string;
}

export const fetchCountries = async (): Promise<Record<string, Country>> => {
  const response = await api.get("/countries");
  return response.data;
};

export const fetchOperators = async (country: string): Promise<Operator[]> => {
  try {
    console.log("API Request - Fetching operators for country:", country);
    const response = await api.get(`/operators/${country}`);

    // Log the complete response structure
    console.group("API Response");
    console.log("Response status:", response.status);
    console.log("Response headers:", response.headers);
    console.log("Response data:", response.data);

    // Check if the response is an array and log each operator's details
    if (Array.isArray(response.data)) {
      console.log("Number of operators:", response.data.length);
      response.data.forEach((op: Operator, index: number) => {
        console.log(`Operator ${index + 1}:`, {
          id: op.id,
          name: op.name,
          products: op.products,
          productsType: Array.isArray(op.products)
            ? "array"
            : typeof op.products,
          productsLength: Array.isArray(op.products)
            ? op.products.length
            : "N/A",
        });
      });
    } else {
      console.log(
        "Unexpected response format. Expected an array of operators."
      );
      console.log("Actual response type:", typeof response.data);
    }
    console.groupEnd();

    return response.data;
  } catch (error) {
    console.error("Error fetching operators:", error);
    if (axios.isAxiosError(error)) {
      console.error("Error details:", {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        headers: error.response?.headers,
      });
    }
    throw error;
  }
};

export interface CreateOrderParams {
  country: string;
  operator: string;
  product: string;
}

export const createOrder = async ({
  country,
  operator,
  product,
}: CreateOrderParams): Promise<OrderResponse> => {
  try {
    console.group("Creating Order");
    console.log("Request payload:", { country, operator, product });

    const response = await api.post("/order", {
      country,
      operator,
      product,
    });

    console.log("Order created successfully:", response.data);
    console.groupEnd();

    return response.data;
  } catch (error) {
    console.error("Error in createOrder:", error);

    if (axios.isAxiosError(error)) {
      console.error("Order creation failed:", {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        headers: error.response?.headers,
        config: {
          url: error.config?.url,
          method: error.config?.method,
          data: error.config?.data,
        },
      });
    }

    throw error;
  }
};
